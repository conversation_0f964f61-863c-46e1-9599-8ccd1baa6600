/**
 * 清理图片URL，移除查询参数
 *
 * 处理以下情况：
 * 1. Next.js 图片 URL: /_next/image?url=https%3A%2F%2Fexample.com%2Fimage.jpg&w=1000&q=75
 * 2. 完整的 Next.js 图片 URL: https://www.notion.so/_next/image?url=...
 * 3. 相对路径: /images/some-image.jpg
 * 4. 带查询参数的 URL: https://example.com/image.jpg?width=500
 *
 * @param {string} url 原始图片URL
 * @returns {string} 清理后的URL
 */
export function cleanImageUrl(url) {
  if (!url) return ''

  try {
    // 处理相对路径
    if (url.startsWith('/')) {
      // 检查是否是 Next.js 图片 URL
      if (url.startsWith('/_next/image?url=')) {
        // 提取并解码原始 URL
        const encodedUrl = url.replace('/_next/image?url=', '').split('&')[0]
        url = decodeURIComponent(encodedUrl)
        if (url.startsWith('/')) {
          url = 'https://www.notion.com' + url
        }
      } else {
        url = 'https://www.notion.com' + url
      }
    }

    // 检查完整 URL 是否是 Next.js 图片 URL
    if (url.includes('/_next/image?url=')) {
      const encodedUrl = url.split('/_next/image?url=')[1].split('&')[0]
      url = decodeURIComponent(encodedUrl)
    }

    // 移除URL中的查询参数
    const urlObj = new URL(url, 'https://www.notion.com')
    urlObj.search = ''
    return urlObj.toString()
  } catch (error) {
    console.error('清理图片URL时出错:', error)
    return url
  }
}

/**
 * 获取YouTube视频的缩略图URL
 * @param {string} videoId YouTube视频ID
 * @returns {string} 缩略图URL
 */
export function getYouTubeThumbnail(videoId) {
  if (!videoId) return ''
  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`
}

// /**
//  * 从HTML中提取所有图片并清理URL
//  * @param {Document} document DOM文档
//  * @returns {Array<string>} 清理后的图片URL数组
//  */
// export function extractImages(document) {
//   if (!document) return []

//   const images = []
//   document.querySelectorAll('img').forEach((img) => {
//     const src = img.getAttribute('src')
//     if (src) {
//       images.push(cleanImageUrl(src))
//     }
//   })

//   return images
// }
