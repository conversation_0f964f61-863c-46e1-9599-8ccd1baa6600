# Notion文档追踪器

这是一个自动化工具，用于将Notion帮助中心的内容同步到Notion数据库，并通过Telegram发送更新通知。

## 功能

- 抓取Notion帮助中心的文档内容
- 将内容同步到Notion数据库（前端和后端数据库）
- 记录更新的文档（包括新增和修改）
- 通过Telegram发送更新通知，包含文档名称和链接
- 支持发送到Telegram群组的特定Topic
- 提供操作按钮，方便查看帮助文档和中文翻译

## 配置

1. 复制`.env.example`文件为`.env`
2. 填写以下配置信息：
   - `NOTION_API_KEY`: Notion API密钥
   - `NOTION_FRONTEND_DATABASE_ID`: 前端数据库ID
   - `NOTION_BACKEND_DATABASE_ID`: 后端数据库ID
   - `TELEGRAM_BOT_TOKEN`: Telegram机器人Token
   - `TELEGRAM_CHAT_ID`: Telegram聊天ID（群组或个人）
   - `TELEGRAM_TOPIC_ID`: Telegram群组中的Topic ID（可选）
   - `HELP_DOCS_URL`: 帮助文档URL（可选，默认为Notion帮助中心）
   - `TRANSLATION_URL`: 中文翻译URL（可选，默认为Notion中文帮助中心）

## 使用方法

```bash
# 正常运行（启用 Telegram 通知）
npm start

# 运行时禁用 Telegram 通知
npm run start:no-telegram

# 或者直接使用命令行参数
node app/index.js --no-telegram
```

### Telegram 通知控制

你可以通过以下方式控制是否发送 Telegram 通知：

1. 使用命令行参数 `--no-telegram` 禁用通知
2. 默认情况下通知是启用的
3. 即使禁用了通知，同步过程仍然会正常进行

## Telegram通知

当有文档更新时，系统会自动向配置的Telegram群组发送通知，包含以下信息：

- 更新时间
- 更新文档数量
- 文档列表（包含emoji、名称和链接）
- 新增文档标记
- 操作按钮（查看帮助文档和中文翻译）

### 发送到Topic

如果配置了`TELEGRAM_TOPIC_ID`，消息将发送到群组中的特定Topic，而不是主聊天区域。这对于大型群组特别有用，可以将更新通知集中在特定的讨论主题中。

## 注意事项

- 确保Notion API密钥具有足够的权限
- Telegram机器人需要添加到目标群组并具有发送消息的权限
- 要使用Topic功能，需要在超级群组中创建Topic并获取其ID
