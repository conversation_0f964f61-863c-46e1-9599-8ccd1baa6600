import { NotionService } from './NotionService.js'
import { DataService } from './DataService.js'
import { CacheService } from './CacheService.js'
import { TelegramService } from './TelegramService.js'
import { FRONTEND_DATABASE_ID, BACKEND_DATABASE_ID, DATABASE_SCHEMA, PAGE_STATUS } from '../config/notion.js'

/**
 * 同步服务，用于协调整个同步过程
 */
export class SyncService {
  constructor(options = {}) {
    this.dataService = new DataService()
    this.notionService = new NotionService()
    this.cacheService = new CacheService()
    this.telegramService = new TelegramService()

    // 添加配置选项
    this.options = {
      enableTelegramNotification: true, // 默认启用通知
      ...options,
    }

    this.batchSize = options.batchSize || 1

    this.failedItems = {
      categories: [],
      articles: [],
    }
    // 记录更新的文档
    this.updatedDocs = []
  }

  /**
   * 初始化缓存
   */
  async initializeCache() {
    console.log('正在获取所有前端页面...')
    const frontendPages = await this.notionService.getAllDatabasePages(FRONTEND_DATABASE_ID)
    console.log(`获取到 ${frontendPages.length} 个前端页面`)

    console.log('正在获取所有后端页面...')
    const backendPages = await this.notionService.getAllDatabasePages(BACKEND_DATABASE_ID)
    console.log(`获取到 ${backendPages.length} 个后端页面`)

    // 初始化缓存
    this.cacheService.initializeFromPages(frontendPages, backendPages)

    return {
      frontendPagesCount: frontendPages.length,
      backendPagesCount: backendPages.length,
    }
  }

  /**
   * 处理分类
   * @param {Object} category 分类对象
   * @returns {Promise<Object>} 处理结果
   */
  async processCategory(category) {
    try {
      console.log(`处理分类: ${category.name}`)

      // 1. 获取页面信息，包括内容和元数据（含哈希值）
      const { metadata: rawMetadata, content } = await this.dataService.processCategoryContent(category.slug)

      // 合并分类信息和内容元数据
      const metadata = {
        ...rawMetadata,
        ...category,
        category: category.name,
        isCategory: true,
      }

      // 2. 构建分类键名
      const categoryKey = `category:${metadata.slug}`

      // 3. 从缓存中查找前端和后端页面
      const frontendPage = this.cacheService.getFrontendPage(categoryKey)
      const backendPage = this.cacheService.getBackendPage(categoryKey)

      let updated = false
      let newFrontendPage = frontendPage
      let newBackendPage = backendPage

      // 4. 处理前端页面
      if (!frontendPage) {
        console.log(`前端分类页面不存在: ${metadata.name}，正在创建...`)
        newFrontendPage = await this.notionService.createFrontendPage({ key: categoryKey, metadata, isCategory: true })
        console.log(`已创建前端分类页面: ${metadata.name}`)
        updated = true
      }

      // 5. 处理后端页面
      if (backendPage) {
        // 后端页面存在，检查是否需要更新
        console.log(`找到已存在的后端分类页面: ${metadata.name}`)

        // 比较哈希值，判断是否需要更新
        const existingHash = backendPage.properties[DATABASE_SCHEMA.backend.hash]?.rich_text[0]?.text.content
        const needsUpdate = existingHash !== metadata.hash

        if (needsUpdate) {
          console.log(`分类内容已变更: ${metadata.name}，正在更新...`)

          // 更新后端页面
          const backendResult = await this.notionService.createOrUpdateBackendPage({
            frontendPageId: newFrontendPage.id,
            backendPageId: backendPage.id,
            content,
            metadata,
          })

          newBackendPage = backendResult.page
          updated = true

          console.log(`后端分类页面已更新: ${metadata.name}`)

          // 更新前端页面状态
          // await this.notionService.markFrontendPageNeedsUpdate(newFrontendPage.id, metadata)
          console.log(`已标记前端分类页面需要更新: ${metadata.name}`)
        } else {
          console.log(`分类内容无变化: ${metadata.name}`)
        }
      } else {
        // 后端页面不存在，创建新的后端页面
        console.log(`后端分类页面不存在: ${metadata.name}，正在创建...`)

        const backendResult = await this.notionService.createOrUpdateBackendPage({
          frontendPageId: newFrontendPage.id,
          content,
          metadata,
        })

        newBackendPage = backendResult.page
        updated = true

        console.log(`已创建后端分类页面: ${metadata.name}`)

        // 新创建的后端页面，标记前端页面需要更新
        // await this.notionService.markFrontendPageNeedsUpdate(newFrontendPage.id, metadata)
        console.log(`已标记前端分类页面需要更新: ${metadata.name}`)
      }

      // 更新缓存
      this.cacheService.updatePages(newFrontendPage, newBackendPage)

      return {
        frontendPage: newFrontendPage,
        backendPage: newBackendPage,
        updated,
      }
    } catch (error) {
      console.error(`处理分类时出错 ${category.name}:`, error)
      // 记录失败的分类和原因
      this.failedItems.categories.push({
        name: category.name,
        slug: category.slug,
        error: error.message || '未知错误',
        stack: error.stack,
      })
      throw error
    }
  }

  /**
   * 处理文章
   * @param {Object} article 文章对象
   * @param {Object} category 分类对象
   * @returns {Promise<Object>} 处理结果
   */
  async processArticle(article, category) {
    try {
      console.log(`处理文章: ${article.name}`)

      // 1. 获取页面信息，包括内容和元数据（含哈希值）
      const { metadata: rawMetadata, content } = await this.dataService.processContent(article, category)

      // 合并文章信息和内容元数据
      const metadata = {
        ...article,
        ...rawMetadata,
      }

      // 2. 构建文章键名
      const articleKey = `help:${article.slug}`

      // 3. 从缓存中获取分类页面ID
      const categoryPageId = this.cacheService.getCategoryPageId(category.slug)
      if (!categoryPageId) {
        throw new Error(`分类 ${category.name} 不存在，请先处理分类`)
      }

      // 4. 从缓存中查找前端和后端页面
      const frontendPage = this.cacheService.getFrontendPage(articleKey)
      const backendPage = this.cacheService.getBackendPage(articleKey)

      let updated = false
      let newFrontendPage = frontendPage
      let newBackendPage = backendPage
      let isNew = false

      // 5. 处理前端页面
      if (!frontendPage) {
        console.log(`前端文章页面不存在: ${metadata.name}，正在创建...`)
        newFrontendPage = await this.notionService.createFrontendPage({
          key: articleKey,
          metadata,
          isCategory: false,
          parentPageId: categoryPageId,
        })
        console.log(`已创建前端文章页面: ${metadata.name}`)
        updated = true
        isNew = true
      }

      // 6. 处理后端页面
      if (backendPage) {
        // 后端页面存在，检查是否需要更新
        console.log(`找到已存在的后端文章页面: ${metadata.name}`)

        // 比较哈希值，判断是否需要更新
        const existingHash = backendPage.properties[DATABASE_SCHEMA.backend.hash]?.rich_text[0]?.text.content
        const needsUpdate = existingHash !== metadata.hash

        if (needsUpdate) {
          console.log(`文章内容已变更: ${metadata.name}，正在更新...`)

          // 更新后端页面
          const backendResult = await this.notionService.createOrUpdateBackendPage({
            frontendPageId: newFrontendPage.id,
            backendPageId: backendPage.id,
            content,
            metadata,
          })

          newBackendPage = backendResult.page
          updated = true

          console.log(`后端文章页面已更新: ${metadata.name}`)

          // 更新前端页面状态
          // await this.notionService.markFrontendPageNeedsUpdate(newFrontendPage.id, metadata)
          console.log(`已标记前端文章页面需要更新: ${metadata.name}`)
        } else {
          console.log(`文章内容无变化: ${metadata.name}`)
        }
      } else {
        // 后端页面不存在，创建新的后端页面
        console.log(`后端文章页面不存在: ${metadata.name}，正在创建...`)

        const backendResult = await this.notionService.createOrUpdateBackendPage({
          frontendPageId: newFrontendPage.id,
          content,
          metadata,
        })

        newBackendPage = backendResult.page
        updated = true
        isNew = true

        console.log(`已创建后端文章页面: ${metadata.name}`)

        // 新创建的后端页面，标记前端页面需要更新
        // await this.notionService.markFrontendPageNeedsUpdate(newFrontendPage.id, metadata)
        console.log(`已标记前端文章页面需要更新: ${metadata.name}`)
      }

      // 更新缓存
      this.cacheService.updatePages(newFrontendPage, newBackendPage)

      // 如果文档有更新或是新文档，记录到更新列表中
      if (updated) {
        // 获取文档URL
        const url = newFrontendPage?.url || null

        // 记录更新的文档
        this.updatedDocs.push({
          name: metadata.name,
          emoji: metadata.icon || '📄',
          url: `https://www.notion.com/help/${metadata.slug}`,
          isNew,
          category: category.name,
        })
      }

      return {
        frontendPage: newFrontendPage,
        backendPage: newBackendPage,
        updated,
      }
    } catch (error) {
      console.error(`处理文章时出错 ${article.name}:`, error)
      // 记录失败的文章和原因
      this.failedItems.articles.push({
        name: article.name,
        slug: article.slug,
        category: category.name,
        error: error.message || '未知错误',
        stack: error.stack,
      })
      throw error
    }
  }

  /**
   * 显示失败项目的详细信息
   */
  displayFailedItems() {
    console.log('\n========== 失败项目详情 ==========')

    if (this.failedItems.categories.length > 0) {
      console.log(`\n失败的分类 (${this.failedItems.categories.length}):`)
      this.failedItems.categories.forEach((item, index) => {
        console.log(`\n${index + 1}. 分类: ${item.name} (${item.slug})`)
        console.log(`   错误: ${item.error}`)
      })
    } else {
      console.log('没有失败的分类')
    }

    if (this.failedItems.articles.length > 0) {
      console.log(`\n失败的文章 (${this.failedItems.articles.length}):`)
      this.failedItems.articles.forEach((item, index) => {
        console.log(`\n${index + 1}. 文章: ${item.name} (${item.slug})`)
        console.log(`   分类: ${item.category}`)
        console.log(`   错误: ${item.error}`)
      })
    } else {
      console.log('没有失败的文章')
    }

    console.log('\n==================================')
  }

  /**
   * 发送更新通知
   */
  async sendUpdateNotification() {
    if (this.updatedDocs.length === 0) {
      console.log('没有文档更新，跳过发送通知')
      return
    }

    // 检查是否启用了 Telegram 通知
    if (!this.options.enableTelegramNotification) {
      console.log('Telegram 通知已禁用，跳过发送通知')
      return
    }

    try {
      console.log(`准备发送 ${this.updatedDocs.length} 个文档更新通知...`)
      const message = this.telegramService.generateUpdateMessage(this.updatedDocs)
      console.log(message)
      await this.telegramService.sendMessage(message)
      console.log('更新通知已发送')
    } catch (error) {
      console.error('发送更新通知时出错:', error)
    }
  }

  /**
   * 开始同步过程
   */
  async start() {
    try {
      console.log('开始同步 Notion 文档...')

      // 1. 初始化缓存
      await this.initializeCache()

      // 2. 获取所有分类和文章数据
      const categories = await this.dataService.fetchAllCategories()
      console.log(`获取到 ${categories.length} 个分类`)

      // 统计信息
      const stats = {
        categories: {
          total: categories.length,
          processed: 0,
          updated: 0,
          failed: 0,
        },
        articles: {
          total: 0,
          processed: 0,
          updated: 0,
          failed: 0,
        },
      }

      // 计算文章总数
      categories.forEach((category) => {
        stats.articles.total += category.articles?.length || 0
      })

      console.log(`总计 ${stats.categories.total} 个分类，${stats.articles.total} 篇文章待处理`)

      // 3. 先处理所有分类
      console.log('\n开始处理分类...')
      for (const category of categories) {
        category.name = category.name.replace(',', '')
        try {
          console.log(`\n[${++stats.categories.processed}/${stats.categories.total}] 处理分类: ${category.name}`)

          // 处理分类
          const categoryResult = await this.processCategory(category)

          if (categoryResult.updated) {
            stats.categories.updated++
          }
        } catch (error) {
          console.error(`处理分类 ${category.name} 时出错:`, error)
          stats.categories.failed++
          // 继续处理下一个分类
        }
      }

      // 4. 再处理所有文章
      console.log('\n开始处理文章...')
      let totalArticleProcessed = 0

      for (const category of categories) {
        const articles = category.articles || []
        console.log(`\n分类 ${category.name} 下有 ${articles.length} 篇文章`)

        let articleProcessed = 0
        // 将文章分组，每组 4 篇
        for (let i = 0; i < articles.length; i += this.batchSize) {
          const currentBatch = articles.slice(i, i + this.batchSize)
          const batchPromises = currentBatch.map(async (article) => {
            try {
              console.log(
                `\n[${++articleProcessed}/${articles.length}] [总进度: ${++totalArticleProcessed}/${stats.articles.total}] 处理文章: ${article.name}`
              )

              const articleResult = await this.processArticle(article, category)
              stats.articles.processed++

              if (articleResult.updated) {
                stats.articles.updated++
              }

              console.log(`文章 ${article.name} 处理完成`)
            } catch (error) {
              console.error(`处理文章 ${article.name} 时出错:`, error)
              stats.articles.failed++
            }
          })

          // 并行处理当前批次的文章
          await Promise.all(batchPromises)
        }
      }

      // 5. 发送更新通知
      if (this.updatedDocs.length > 0) {
        console.log(`\n发送 ${this.updatedDocs.length} 个文档更新通知...`)
        await this.sendUpdateNotification()
      } else {
        console.log('\n没有文档更新，跳过发送通知')
      }

      // 输出统计信息
      console.log('\n========== 同步完成 ==========')
      console.log(
        `分类: 总计 ${stats.categories.total}，处理 ${stats.categories.processed}，更新 ${stats.categories.updated}，失败 ${stats.categories.failed}`
      )
      console.log(
        `文章: 总计 ${stats.articles.total}，处理 ${stats.articles.processed}，更新 ${stats.articles.updated}，失败 ${stats.articles.failed}`
      )

      // 显示失败项目的详细信息
      if (stats.categories.failed > 0 || stats.articles.failed > 0) {
        this.displayFailedItems()
      }

      return stats
    } catch (error) {
      console.error('同步过程中出错:', error)
      throw error
    }
  }
}
