/**
 * 从元素中提取纯文本内容
 * @param {Element} element DOM元素
 * @returns {string} 提取的文本内容
 */
export function extractTextContent(element) {
  if (!element) return ''

  // 创建一个克隆，以便我们可以修改它而不影响原始元素
  const clone = element.cloneNode(true)

  // 移除脚本和样式标签
  clone.querySelectorAll('script, style, noscript').forEach((el) => el.remove())

  return clone.textContent.trim()
}

/**
 * 尝试多个选择器，返回第一个匹配的元素
 * @param {Document} document DOM文档
 * @param {Array<string>} selectors 选择器数组
 * @returns {Element|null} 匹配的元素或null
 */
export function trySelectors(document, selectors) {
  if (!document || !selectors || !selectors.length) return null

  for (const selector of selectors) {
    try {
      const element = document.querySelector(selector)
      if (element) return element
    } catch (error) {
      console.error(`选择器 "${selector}" 出错:`, error)
    }
  }

  return null
}

/**
 * 清理HTML文档，移除不需要的元素
 * @param {Document} document DOM文档
 * @param {Array<string>} selectorsToRemove 要移除的元素的选择器数组
 */
export function cleanDocument(document, selectorsToRemove = []) {
  if (!document) return

  const defaultSelectorsToRemove = [
    'script',
    'style',
    'noscript',
    '.notion-presence-container',
    '[class*="adBlocker"]',
    '[class*="videoPlayer"]',
  ]

  const allSelectors = [...defaultSelectorsToRemove, ...selectorsToRemove]

  document.querySelectorAll(allSelectors.join(',')).forEach((el) => el.remove())
}

/**
 * 从文档中提取元数据
 * @param {Document} document DOM文档
 * @returns {Object} 提取的元数据
 */
export function extractMetadata(document) {
  if (!document) return {}

  // 提取标题
  const title = document.querySelector('h1')?.textContent.trim() || ''

  // 提取描述
  const descriptionSelectors = [
    'article section section header h2',
    'header h2.helpArticle_helpArticlePrologueCopy',
    'article header h2[class*="helpArticlePrologueCopy"]',
    '.helpArticlePrologueCopy',
    'header h2',
    'article > section > section > header > h2',
    'meta[name="description"]',
  ]

  let description = ''
  const descriptionElement = trySelectors(document, descriptionSelectors)

  if (descriptionElement) {
    if (descriptionElement.tagName === 'META') {
      description = descriptionElement.getAttribute('content') || ''
    } else {
      description = descriptionElement.textContent.trim()
    }
  }

  return {
    title,
    description,
  }
}

/**
 * 处理文档，包括清理、处理受保护的电子邮件和链接等
 * @param {Document} document DOM文档
 * @param {Object} options 选项
 * @param {boolean} options.processEmails 是否处理受保护的电子邮件
 * @param {boolean} options.processLinks 是否处理链接
 * @param {boolean} options.cleanDom 是否清理DOM
 * @param {Array<string>} options.selectorsToRemove 要移除的选择器
 * @returns {Document} 处理后的文档
 */
export function processDocument(document, options = {}) {
  if (!document) return document

  const { processEmails = true, processLinks = true, cleanDom = false, selectorsToRemove = [] } = options

  // 清理DOM
  if (cleanDom) {
    cleanDocument(document, selectorsToRemove)
  }

  // 处理受保护的电子邮件
  if (processEmails) {
    // 处理带有data-cfemail属性的元素（Cloudflare的主要保护方式）
    const cfEmailElements = document.querySelectorAll('[data-cfemail], .__cf_email__')
    if (cfEmailElements.length > 0) {
      console.log(`找到 ${cfEmailElements.length} 个带有 data-cfemail 的元素`)
      cfEmailElements.forEach((el) => {
        // 创建一个文本节点替换原元素
        const textNode = document.createTextNode('email protected')
        el.parentNode.replaceChild(textNode, el)
      })
    }

    const linkElements = document.querySelectorAll('a')
    linkElements.forEach((el) => {
      if (el.href && el.href.startsWith('/cdn-cgi/l/email-protection')) {
        // 创建一个文本节点替换原元素
        const textNode = document.createTextNode('email protected')
        el.parentNode.replaceChild(textNode, el)
      }
    })
  }

  // 处理链接
  if (processLinks) {
    document.querySelectorAll('a').forEach((link) => {
      const href = link.getAttribute('href')
      if (href && href.startsWith('/')) {
        link.setAttribute('href', 'https://www.notion.com' + href)
      }
    })
  }

  return document
}

// /**
//  * 处理Cloudflare保护的电子邮件地址
//  * @param {Document} document DOM文档
//  */
// export function handleProtectedEmails(document) {
//   return processDocument(document, { processEmails: true, processLinks: false })
// }

// /**
//  * 处理文档中的所有链接，将以/开头的链接转换为Notion域名链接
//  * @param {Document} document DOM文档
//  * @returns {Document} 处理后的文档
//  */
// export function processDocumentLinks(document) {
//   return processDocument(document, { processEmails: false, processLinks: true })
// }
