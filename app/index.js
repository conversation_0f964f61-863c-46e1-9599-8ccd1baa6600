import { SyncService } from './services/SyncService.js'

/**
 * Notion文档同步工具
 * 将Notion帮助中心的内容同步到Notion数据库
 */
async function main() {
  // 解析命令行参数
  const args = process.argv.slice(2)
  const options = {
    batchSize: 5,
    enableTelegramNotification: !args.includes('--no-telegram'),
    showDuration: true,
  }

  try {
    // 记录开始时间
    const startTime = options.showDuration ? Date.now() : null

    console.log('开始同步...')
    console.log(`Telegram 通知已${options.enableTelegramNotification ? '启用' : '禁用'}`)

    const syncService = new SyncService(options)
    await syncService.start()

    if (options.showDuration) {
      const endTime = Date.now()
      const duration = (endTime - startTime) / 1000
      console.log(`总耗时: ${duration.toFixed(2)}秒`)
    }
    console.log('==============================')
  } catch (error) {
    console.error('同步过程出错:', error)
    process.exit(1)
  }
}

// 启动应用
main()
