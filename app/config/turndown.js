import TurndownService from 'turndown'

/**
 * 配置 Turndown 服务，用于将 HTML 转换为 Markdown
 * @returns {TurndownService} 配置好的 Turndown 服务实例
 */
export function configureMarkdown() {
  const turndownService = new TurndownService({
    headingStyle: 'atx',
    codeBlockStyle: 'fenced',
  })

  // 配置规则
  turndownService.addRule('removeEmptyParagraphs', {
    filter: (node) => {
      return node.nodeName === 'P' && node.textContent.trim() === '' && !node.querySelector('img')
    },
    replacement: () => '',
  })

  // 保留图片
  turndownService.addRule('preserveImages', {
    filter: 'img',
    replacement: function (content, node) {
      const alt = node.getAttribute('alt') || ''
      const src = node.getAttribute('src') || ''
      if (!src) return ''
      return `![${alt}](${src})`
    },
  })

  // 保留表格格式
  turndownService.addRule('tables', {
    filter: ['table'],
    replacement: function (content, node) {
      const rows = Array.from(node.querySelectorAll('tr'))
      if (rows.length === 0) return ''

      // 处理表头
      const headers = Array.from(rows[0].querySelectorAll('th')).map((th) => th.textContent.trim())
      if (headers.length === 0) return content

      // 创建表格标记
      let markdown = '\n'
      markdown += `| ${headers.join(' | ')} |\n`
      markdown += `| ${headers.map(() => '---').join(' | ')} |\n`

      // 处理表格内容
      for (let i = 1; i < rows.length; i++) {
        const cells = Array.from(rows[i].querySelectorAll('td')).map((td) => td.textContent.trim())
        if (cells.length > 0) {
          markdown += `| ${cells.join(' | ')} |\n`
        }
      }

      return markdown
    },
  })

  // 保留代码块
  turndownService.addRule('codeBlocks', {
    filter: function (node) {
      return node.nodeName === 'PRE' && node.querySelector('code')
    },
    replacement: function (content, node) {
      const code = node.querySelector('code')
      const language = code.className.replace('language-', '')
      const codeContent = code.textContent.trim()
      return `\n\`\`\`${language}\n${codeContent}\n\`\`\`\n`
    },
  })

  // 处理内联代码
  turndownService.addRule('inlineCode', {
    filter: function (node) {
      return node.nodeName === 'CODE' && node.parentNode.nodeName !== 'PRE'
    },
    replacement: function (content) {
      return '`' + content + '`'
    },
  })

  return turndownService
}
