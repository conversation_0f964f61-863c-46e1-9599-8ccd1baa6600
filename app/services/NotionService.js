import { Client } from '@notionhq/client'
import crypto from 'crypto'
import {
  NOTION_API_KEY,
  DATABASE_SCHEMA,
  PAGE_STATUS,
  FRONTEND_DATABASE_ID,
  BACKEND_DATABASE_ID,
} from '../config/notion.js'

export class NotionService {
  constructor() {
    this.client = new Client({ auth: NOTION_API_KEY })
  }

  async createPage(databaseId, properties) {
    try {
      const createData = {
        parent: { database_id: databaseId },
        properties: { ...properties },
      }

      // 如果properties中包含icon属性，将其提取出来并放到properties外面
      if (properties.icon) {
        createData.icon = {
          type: 'emoji',
          emoji: properties.icon,
        }
        // 从properties中删除icon属性
        delete createData.properties.icon
      }

      return await this.client.pages.create(createData)
    } catch (error) {
      console.error('Error creating page:', error)
      throw error
    }
  }

  async updatePage(pageId, properties) {
    try {
      const updateData = {
        page_id: pageId,
        properties: { ...properties },
      }

      // 如果properties中包含icon属性，将其提取出来并放到properties外面
      if (properties.icon) {
        updateData.icon = {
          type: 'emoji',
          emoji: properties.icon,
        }
        // 从properties中删除icon属性
        delete updateData.properties.icon
      }

      return await this.client.pages.update(updateData)
    } catch (error) {
      console.error('Error updating page:', error)
      throw error
    }
  }

  // async findPageByKey(key, databaseId = FRONTEND_DATABASE_ID) {
  //   try {
  //     const response = await this.client.databases.query({
  //       database_id: databaseId,
  //       filter: {
  //         property: databaseId === FRONTEND_DATABASE_ID ? DATABASE_SCHEMA.frontend.key : DATABASE_SCHEMA.backend.key,
  //         rich_text: {
  //           equals: key,
  //         },
  //       },
  //     })
  //     return response.results[0] || null
  //   } catch (error) {
  //     console.error('Error finding page:', error)
  //     throw error
  //   }
  // }

  // async findBackendPageByKey(key) {
  //   return await this.findPageByKey(key, BACKEND_DATABASE_ID)
  // }

  // async getFrontendPageFromBackendPage(backendPage) {
  //   if (!backendPage || !backendPage.properties[DATABASE_SCHEMA.backend.reference]) {
  //     return null
  //   }

  //   try {
  //     const relationId = backendPage.properties[DATABASE_SCHEMA.backend.reference].relation[0]?.id
  //     if (!relationId) {
  //       return null
  //     }

  //     return await this.client.pages.retrieve({ page_id: relationId })
  //   } catch (error) {
  //     console.error('Error getting frontend page from backend page:', error)
  //     return null
  //   }
  // }

  generateHash(content) {
    return crypto.createHash('md5').update(content).digest('hex')
  }

  async createFrontendPage({ key, metadata, isCategory = false, parentPageId = null }) {
    const properties = this._generateFrontendProperties(key, metadata, isCategory)

    // 创建页面数据
    const createData = {
      parent: parentPageId ? { page_id: parentPageId } : { database_id: FRONTEND_DATABASE_ID },
      properties,
    }

    // 如果元数据中包含图标，添加到页面
    if (metadata.icon) {
      createData.icon = { type: 'emoji', emoji: metadata.icon }
    }

    try {
      return await this.client.pages.create(createData)
    } catch (error) {
      console.error('Error creating frontend page:', error)
      throw error
    }
  }

  // async updateFrontendPage(pageId, key, metadata, isCategory = false, status = PAGE_STATUS.NEEDS_UPDATE) {
  //   const properties = {
  //     [DATABASE_SCHEMA.frontend.status]: {
  //       status: { name: status },
  //     },
  //   }

  //   if (metadata.category) {
  //     properties[DATABASE_SCHEMA.frontend.category] = {
  //       select: { name: metadata.category },
  //     }
  //   }

  //   return await this.updatePage(pageId, properties)
  // }

  async markFrontendPageNeedsUpdate(pageId, metadata) {
    const properties = {
      [DATABASE_SCHEMA.frontend.status]: {
        status: { name: PAGE_STATUS.NEEDS_UPDATE },
      },
      [DATABASE_SCHEMA.frontend.category]: {
        select: { name: metadata.category },
      },
      [DATABASE_SCHEMA.frontend.url]: {
        url: metadata.isCategory
          ? `https://www.notion.com/help/category/${metadata.slug}`
          : `https://www.notion.com/help/${metadata.slug}`,
      },
      ...(metadata.coverImage && {
        [DATABASE_SCHEMA.frontend.cover]: {
          files: [
            {
              type: 'external',
              name: 'cover',
              external: {
                url: metadata.coverImage,
              },
            },
          ],
        },
      }),
    }

    // 如果有分类信息，添加到属性中
    // if (metadata.category && !metadata.isCategory) {
    //   properties[DATABASE_SCHEMA.frontend.category] = {
    //     select: { name: metadata.category },
    //   }
    // }

    // 如果有图标，添加到属性中
    if (metadata.icon) {
      properties.icon = metadata.icon
    }

    return await this.updatePage(pageId, properties)
  }

  _splitContentForNotion(content) {
    return content.split('').reduce((acc, char) => {
      if (!acc.length || acc[acc.length - 1].text.content.length >= 1800) {
        acc.push({ text: { content: char } })
      } else {
        acc[acc.length - 1].text.content += char
      }
      return acc
    }, [])
  }

  _generateFrontendProperties(key, metadata, isCategory = false) {
    if (!key) {
      throw new Error('Key is required for frontend page properties')
    }

    const properties = {
      [DATABASE_SCHEMA.frontend.name]: {
        title: [{ text: { content: metadata.name || '' } }],
      },
      [DATABASE_SCHEMA.frontend.key]: {
        rich_text: [{ text: { content: key } }],
      },
      ...(metadata.coverImage && {
        [DATABASE_SCHEMA.frontend.cover]: {
          files: [
            {
              type: 'external',
              name: 'cover',
              external: {
                url: metadata.coverImage,
              },
            },
          ],
        },
      }),
      [DATABASE_SCHEMA.frontend.status]: {
        status: { name: PAGE_STATUS.NOT_STARTED },
      },
      [DATABASE_SCHEMA.frontend.category]: {
        select: { name: metadata.category },
      },
      [DATABASE_SCHEMA.frontend.url]: {
        url: metadata.isCategory
          ? `https://www.notion.com/help/category/${metadata.slug}`
          : `https://www.notion.com/help/${metadata.slug}`,
      },
    }

    if (isCategory) {
      properties[DATABASE_SCHEMA.frontend.category] = {
        select: { name: 'Category' },
      }
    } else if (metadata.category) {
      properties[DATABASE_SCHEMA.frontend.category] = {
        select: { name: metadata.category },
      }
    }

    return properties
  }

  async createOrUpdateBackendPage({ frontendPageId, backendPageId = null, content, metadata }) {
    try {
      const properties = {
        [DATABASE_SCHEMA.backend.name]: {
          title: [{ text: { content: metadata.name } }],
        },
        [DATABASE_SCHEMA.backend.category]: {
          select: { name: metadata.category },
        },
        [DATABASE_SCHEMA.backend.url]: {
          url: metadata.isCategory
            ? `https://www.notion.com/help/category/${metadata.slug}`
            : `https://www.notion.com/help/${metadata.slug}`,
        },
        [DATABASE_SCHEMA.backend.key]: {
          rich_text: [
            { text: { content: metadata.isCategory ? 'category:' + metadata.slug : 'help:' + metadata.slug } },
          ],
        },
        [DATABASE_SCHEMA.backend.content]: {
          rich_text: this._splitContentForNotion(content),
        },
        // 用于调试时，暂时注释掉 Hash 部分
        [DATABASE_SCHEMA.backend.hash]: {
          rich_text: [{ text: { content: metadata.hash } }],
        },
        [DATABASE_SCHEMA.backend.reference]: {
          relation: [{ id: frontendPageId }],
        },
        [DATABASE_SCHEMA.backend.status]: {
          status: { name: backendPageId ? PAGE_STATUS.NEEDS_UPDATE : PAGE_STATUS.NOT_STARTED },
        },
        ...(metadata.icon && {
          icon: metadata.icon,
        }),
      }

      if (backendPageId) {
        return {
          page: await this.updatePage(backendPageId, properties),
          updated: true,
        }
      }

      // 如果没有找到现有页面，创建新页面
      return {
        page: await this.createPage(BACKEND_DATABASE_ID, properties),
        updated: true, // 创建新页面也应该被视为"更新"
      }
    } catch (error) {
      console.error('Error in createOrUpdateBackendPage:', error)
      throw error
    }
  }

  /**
   * 获取数据库中的所有页面
   * @param {string} databaseId 数据库ID
   * @returns {Promise<Array>} 页面数组
   */
  async getAllDatabasePages(databaseId) {
    try {
      let allPages = []
      let hasMore = true
      let startCursor = undefined

      console.log(`开始获取数据库 ${databaseId} 的所有页面...`)

      while (hasMore) {
        const response = await this.client.databases.query({
          database_id: databaseId,
          start_cursor: startCursor,
          page_size: 100, // 最大值
        })

        allPages = [...allPages, ...response.results]
        hasMore = response.has_more
        startCursor = response.next_cursor

        console.log(`已获取 ${allPages.length} 个页面${hasMore ? '，继续获取...' : ''}`)
      }

      return allPages
    } catch (error) {
      console.error('获取所有页面时出错:', error)
      throw error
    }
  }
}
