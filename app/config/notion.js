import dotenv from 'dotenv'

dotenv.config()

export const NOTION_API_KEY = process.env.NOTION_API_KEY
export const FRONTEND_DATABASE_ID = process.env.NOTION_FRONTEND_DATABASE_ID
export const BACKEND_DATABASE_ID = process.env.NOTION_BACKEND_DATABASE_ID

export const DATABASE_SCHEMA = {
  frontend: {
    name: 'Name',
    key: 'Key',
    category: 'Category',
    status: 'Status',
    cover: 'Cover',
    url: 'URL',
  },
  backend: {
    name: 'Name',
    category: 'Category',
    key: 'Key',
    content: 'Content',
    hash: 'Hash',
    reference: 'Reference',
    status: 'Status',
    url: 'URL',
  },
}

export const PAGE_STATUS = {
  ACTIVE: 'Done', // 正常显示
  NEEDS_UPDATE: 'In progress', // 需要更新
  NOT_STARTED: 'Not started', // 待办
}
