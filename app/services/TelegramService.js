import dotenv from 'dotenv'

dotenv.config()

/**
 * Telegram服务，用于发送消息到Telegram群组
 */
export class TelegramService {
  constructor() {
    this.botToken = process.env.TELEGRAM_BOT_TOKEN
    this.chatId = process.env.TELEGRAM_CHAT_ID
    this.messageThreadId = process.env.TELEGRAM_TOPIC_ID
    this.helpDocsUrl = process.env.HELP_DOCS_URL || 'https://www.notion.so/help'
    this.translationUrl = process.env.TRANSLATION_URL || 'https://www.notion.so/zh-cn/help'

    if (!this.botToken || !this.chatId) {
      console.warn('警告: 未配置Telegram Bot Token或Chat ID，无法发送Telegram消息')
    }
  }

  /**
   * 发送消息到Telegram群组
   * @param {string} message 消息内容（支持MarkdownV2格式）
   * @returns {Promise<Object>} 发送结果
   */
  async sendMessage(message) {
    if (!this.botToken || !this.chatId) {
      console.warn('未配置Telegram Bot Token或Chat ID，跳过发送消息')
      return null
    }

    try {
      const url = `https://api.telegram.org/bot${this.botToken}/sendMessage`

      // 准备请求体
      const requestBody = {
        chat_id: this.chatId,
        text: message,
        parse_mode: 'MarkdownV2',
        disable_web_page_preview: true,
        // 添加内联键盘按钮
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: '📚 查看帮助文档',
                url: this.helpDocsUrl,
              },
              {
                text: '🀄 查看中文翻译',
                url: this.translationUrl,
              },
            ],
          ],
        },
      }

      // 如果配置了Topic ID，则添加到请求中
      if (this.messageThreadId) {
        requestBody.message_thread_id = this.messageThreadId
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(`Telegram API错误: ${JSON.stringify(errorData)}`)
      }

      return await response.json()
    } catch (error) {
      console.error('发送Telegram消息时出错:', error)
      throw error
    }
  }

  /**
   * 转义MarkdownV2特殊字符
   * @param {string} text 原始文本
   * @returns {string} 转义后的文本
   */
  escapeMarkdown(text) {
    return text.replace(/([_*[\]()~`>#+\-=|{}.!])/g, '\\$1')
  }

  /**
   * 生成文档更新消息
   * @param {Array} updatedDocs 更新的文档列表
   * @returns {string} 格式化的消息
   */
  generateUpdateMessage(updatedDocs) {
    if (!updatedDocs || updatedDocs.length === 0) {
      return '没有文档更新'
    }

    // 标题
    let message = '📝 *Notion 帮助文档更新* 📝\n\n'

    // 更新时间
    // const now = new Date()
    // const dateStr = now.toLocaleDateString('zh-CN')
    // const timeStr = now.toLocaleTimeString('zh-CN')
    // message += `更新时间: ${this.escapeMarkdown(dateStr)} ${this.escapeMarkdown(timeStr)}\n\n`

    // 更新文档列表
    message += `共有 ${updatedDocs.length} 篇文档更新:\n\n`

    updatedDocs.forEach((doc, index) => {
      const emoji = doc.emoji || '📄'
      const name = this.escapeMarkdown(doc.name)
      const url = doc.url ? `[${emoji} ${name}](${this.escapeMarkdown(doc.url)})` : `${emoji} ${name}`
      const isNewTag = doc.isNew ? ' \\[*新增*\\]' : ''

      message += `${index + 1}\\. ${url}${isNewTag}\n`
    })

    return message
  }
}
