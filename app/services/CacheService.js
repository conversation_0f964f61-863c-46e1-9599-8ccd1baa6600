import { DATABASE_SCHEMA } from '../config/notion.js'

/**
 * 缓存服务，用于管理Notion页面的本地缓存
 */
export class CacheService {
  constructor() {
    this.frontendPages = new Map() // 键为页面key，值为页面对象
    this.backendPages = new Map()
    this.frontendPageIds = new Map() // 键为分类slug，值为页面ID
  }

  /**
   * 从获取的页面列表初始化缓存
   * @param {Array} frontendPages 前端页面列表
   * @param {Array} backendPages 后端页面列表
   */
  initializeFromPages(frontendPages, backendPages) {
    // 初始化前端页面缓存
    frontendPages.forEach((page) => {
      const key = page.properties[DATABASE_SCHEMA.frontend.key]?.rich_text[0]?.text.content
      if (key) {
        this.frontendPages.set(key, page)

        // 如果是分类页面，记录ID
        if (key.startsWith('category:')) {
          const slug = key.replace('category:', '')
          this.frontendPageIds.set(slug, page.id)
        }
      }
    })

    // 初始化后端页面缓存
    backendPages.forEach((page) => {
      const key = page.properties[DATABASE_SCHEMA.backend.key]?.rich_text[0]?.text.content
      if (key) {
        this.backendPages.set(key, page)
      }
    })

    console.log(`缓存初始化完成: ${this.frontendPages.size} 个前端页面, ${this.backendPages.size} 个后端页面`)
  }

  /**
   * 获取前端页面
   * @param {string} key 页面键名
   * @returns {Object|null} 页面对象或null
   */
  getFrontendPage(key) {
    return this.frontendPages.get(key)
  }

  /**
   * 获取后端页面
   * @param {string} key 页面键名
   * @returns {Object|null} 页面对象或null
   */
  getBackendPage(key) {
    return this.backendPages.get(key)
  }

  /**
   * 获取分类页面ID
   * @param {string} slug 分类slug
   * @returns {string|null} 页面ID或null
   */
  getCategoryPageId(slug) {
    return this.frontendPageIds.get(slug)
  }

  /**
   * 更新缓存中的页面
   * @param {Object|null} frontendPage 前端页面对象
   * @param {Object|null} backendPage 后端页面对象
   */
  updatePages(frontendPage, backendPage) {
    if (frontendPage) {
      const key = frontendPage.properties[DATABASE_SCHEMA.frontend.key]?.rich_text[0]?.text.content
      if (key) {
        this.frontendPages.set(key, frontendPage)

        if (key.startsWith('category:')) {
          const slug = key.replace('category:', '')
          this.frontendPageIds.set(slug, frontendPage.id)
        }
      }
    }

    if (backendPage) {
      const key = backendPage.properties[DATABASE_SCHEMA.backend.key]?.rich_text[0]?.text.content
      if (key) {
        this.backendPages.set(key, backendPage)
      }
    }
  }
}
