import { parseHTML } from 'linkedom'
import crypto from 'crypto'
import { configureMarkdown } from '../config/turndown.js'
import { cleanImageUrl, getYouTubeThumbnail } from '../utils/image.js'
import { extractMetadata, processDocument } from '../utils/dom.js'

export class DataService {
  constructor() {
    this.turndownService = configureMarkdown()
  }

  /**
   * 获取所有分类和文章数据
   * @returns {Promise<Array>} 分类数组，每个分类包含其文章
   */
  async fetchAllCategories() {
    try {
      const headers = {
        accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'accept-language': 'en-US,en;q=0.6',
        'cache-control': 'max-age=0',
        'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Brave";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'sec-gpc': '1',
        'upgrade-insecure-requests': '1',
        Referer: 'https://www.notion.com/',
        'Referrer-Policy': 'strict-origin',
        cookie: 'notion_locale=en-US/user_choice',
      }

      const response = await fetch('https://www.notion.com/help', {
        headers,
        method: 'GET',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const html = await response.text()
      let { document } = parseHTML(html)
      const nextDataScript = document.getElementById('__NEXT_DATA__')

      if (!nextDataScript) {
        throw new Error('无法找到 NEXT_DATA')
      }

      const nextData = JSON.parse(nextDataScript.textContent)
      const helpArticleTree = nextData.props.pageProps.helpArticleTree

      // 处理分类和文章数据
      const categories = []

      // 遍历分类
      for (const categoryData of helpArticleTree) {
        // 跳过不需要的分类
        if (!categoryData.entries || categoryData.entries.length === 0) {
          continue
        }

        const category = {
          name: categoryData.name,
          slug: categoryData.slug,
          articles: [],
        }

        // 处理分类下的文章
        for (const articleData of categoryData.entries) {
          // 跳过隐藏的文章
          if (articleData.hidden) {
            continue
          }

          const article = {
            name: articleData.name,
            slug: articleData.slug,
            // 确保URL格式正确
            url: articleData.slug.startsWith('/') ? articleData.slug.substring(1) : articleData.slug,
            icon: articleData.emoji || null, // 使用文章自带的图标
          }

          category.articles.push(article)
        }

        categories.push(category)
      }

      return categories
    } catch (error) {
      console.error('获取分类和文章数据时出错:', error)
      throw error
    }
  }

  /**
   * 处理文章内容
   * @param {Object} article 文章对象
   * @param {Object} category 分类对象
   * @returns {Promise<Object>} 包含内容和元数据的对象
   */
  async processContent(article, category) {
    try {
      // 确保slug格式正确
      const formattedSlug = article.slug.startsWith('/') ? article.slug.substring(1) : article.slug

      // 获取文章内容
      const { content: originalContent, metadata } = await this.fetchArticleContent(formattedSlug)

      // 添加 front-matter
      const frontMatter = `---
title: ${metadata.name || ''}
description: ${metadata.description || ''}
category: ${category.name || ''}
emoji: ${article.icon || ''}
coverImage: ${metadata.coverImage || ''}
---
`

      // 将 front-matter 添加到内容前面
      const content = [frontMatter, `# ${metadata.name}`, originalContent].join('\n\n')

      // 生成内容的哈希值
      const hash = this.generateHash(content)

      // 返回处理后的内容和元数据
      return {
        content,
        metadata: {
          ...metadata,
          name: article.name,
          slug: formattedSlug,
          category: category?.name || '',
          isCategory: false,
          hash,
        },
      }
    } catch (error) {
      console.error(`处理文章内容时出错 (${article.name}):`, error)
      throw error
    }
  }

  /**
   * 处理分类内容
   * @param {string} slug 分类的slug
   * @returns {Promise<Object>} 包含内容和元数据的对象
   */
  async processCategoryContent(slug) {
    try {
      // 获取分类页面内容
      const response = await fetch(`https://www.notion.com/help/category/${slug}`, {
        headers: {
          cookie: 'notion_locale=en-US/user_choice',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const html = await response.text()
      let { document } = parseHTML(html)

      // 处理文档（包括电子邮件保护和链接）
      document = processDocument(document)

      // 获取标题
      const title = document.querySelector('h1[class*="title_title"]')?.textContent || slug

      // 获取封面图片
      let coverImage = ''
      const heroMediaSection = [...document.querySelectorAll('section')].find((item) =>
        item.className.startsWith('_slug__helpCategoryHeroMedia')
      )
      if (heroMediaSection) {
        const heroImage = heroMediaSection.querySelector('img')
        if (heroImage) {
          coverImage = cleanImageUrl(heroImage.src)
        }
      }

      // 获取描述
      let description = ''
      const descriptionElement = document.querySelector('h3')
      if (descriptionElement && descriptionElement.className.includes('title_titleSizeXs')) {
        description = descriptionElement.textContent.trim()
      }

      // 构建分类描述内容
      const frontMatter = `---
title: ${title}
description: ${description}
slug: ${slug}
coverImage: ${coverImage}
---
`

      // 构建内容部分（所有文章名称）
      const posts = [...document.querySelectorAll('header h3')]
      posts.splice(0, 1)
      const postNames = Array.from(posts).map((post) => post.textContent.trim())

      // 将 front-matter 添加到内容前面
      const content = [frontMatter, `# ${title}\n\n${description}`, '---', ...postNames].join('\n\n')

      // 生成内容的哈希值
      const hash = this.generateHash(content)

      // 返回处理后的内容和元数据
      return {
        content,
        metadata: {
          name: title,
          slug,
          description,
          coverImage,
          isCategory: true,
          hash,
        },
      }
    } catch (error) {
      console.error(`处理分类内容时出错 (${slug}):`, error)
      throw error
    }
  }

  /**
   * 获取文章内容
   * @param {string} slug 文章的 slug
   * @returns {Promise<Object>} 包含内容和元数据的对象
   */
  async fetchArticleContent(slug) {
    try {
      // 确保 slug 格式正确
      const formattedSlug = slug.startsWith('/') ? slug.substring(1) : slug

      const response = await fetch(`https://www.notion.com/help/${formattedSlug}`, {
        headers: {
          cookie: 'notion_locale=en-US/user_choice',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const html = await response.text()
      let { document } = parseHTML(html)

      // 处理文档（包括电子邮件保护和链接）
      document = processDocument(document)

      // 清理带有 ?g-exp 参数的链接
      document.querySelectorAll('a[href*="?g-exp"]').forEach((link) => {
        const url = new URL(link.href)
        url.searchParams.delete('g-exp')
        link.href = url.toString()
      })

      // 提取元数据
      const basicMetadata = extractMetadata(document)
      const name = basicMetadata.title
      const description = basicMetadata.description

      // 获取封面图片
      let coverImage = ''
      let isVideoHero = false

      try {
        const heroMediaSection = [...document.querySelectorAll('section')].find((item) =>
          item.className.startsWith('helpArticle_helpArticleHeroMedia')
        )

        if (heroMediaSection) {
          // 检查是否是视频封面
          isVideoHero = !!heroMediaSection.childNodes?.[0]?.className?.startsWith('videoPlayer_videoContainer')

          if (isVideoHero) {
            const videoId = heroMediaSection.childNodes?.[0]?.getAttribute('id')
            coverImage = getYouTubeThumbnail(videoId)
          } else {
            const heroImage = heroMediaSection.querySelector('img')
            if (heroImage) {
              coverImage = cleanImageUrl(heroImage.src)
            }
          }
        }
      } catch (error) {
        console.error('获取封面图片时出错:', error)
      }

      // 获取主要内容
      const article = document.querySelector('article')
      const contentArticle = article?.querySelector('article')

      if (!contentArticle) {
        throw new Error('无法找到文章内容')
      }

      // 处理文章中的所有图片
      contentArticle.querySelectorAll('img').forEach((img) => {
        img.src = cleanImageUrl(img.src)
      })

      // 使用 Turndown 将 HTML 转换为 Markdown
      let mainContent = this.turndownService.turndown(contentArticle)

      // 处理 FAQ 部分
      const faqSection = document.querySelector('article#faq')
      let faqContent = ''

      if (faqSection) {
        const faqItems = Array.from(faqSection.children).filter((item) => item.querySelector('details summary p'))

        const faqParts = []
        for (const faqItem of faqItems) {
          try {
            const question = faqItem.querySelector('summary p')?.textContent
            const answerElement = faqItem.querySelector('article')
            if (question && answerElement) {
              const answer = this.turndownService.turndown(answerElement)
              faqParts.push(`## ${question}\n\n${answer}\n`)
            }
          } catch (error) {
            console.error('处理 FAQ 项时出错:', error)
          }
        }
        faqContent = faqParts.join('\n\n')
      }

      // 组合最终内容
      const content = [mainContent, faqContent].filter(Boolean).join('\n\n')

      // 规范化内容
      const normalizedContent =
        content
          .replace(/!\[\]\(https:\/\/undefined\)/g, '')
          .replace(/\n{3,}/g, '\n\n')
          .trim() + '\n'

      return {
        content: normalizedContent,
        metadata: {
          name,
          coverImage,
          isVideoHero,
          description,
        },
      }
    } catch (error) {
      console.error(`获取文章内容时出错 (${slug}):`, error)
      throw error
    }
  }

  /**
   * 生成内容的MD5哈希值
   * @param {string} content 内容
   * @returns {string} MD5哈希值
   */
  generateHash(content) {
    return crypto.createHash('md5').update(content).digest('hex')
  }
}
